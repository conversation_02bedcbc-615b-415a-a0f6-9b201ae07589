from aiogram.types import InlineKeyboardMarkup, InlineKeyboardButton

from common.keyboards import get_main_menu_back_button


def get_curator_subjects_kb() -> InlineKeyboardMarkup:
    """Клавиатура выбора предмета для связи с куратором"""
    return InlineKeyboardMarkup(inline_keyboard=[
        [InlineKeyboardButton(text="📗 Химия", callback_data="curator_chem")],
        [InlineKeyboardButton(text="📘 Биология", callback_data="curator_bio")],
        [InlineKeyboardButton(text="📕 История Казахстана", callback_data="curator_kz")],
        [InlineKeyboardButton(text="📐 Матемграмотность", callback_data="curator_mathlit")],
        *get_main_menu_back_button()
    ])

def get_back_to_curator_kb() -> InlineKeyboardMarkup:
    """Клавиатура для возврата к выбору предмета куратора"""
    return InlineKeyboardMarkup(inline_keyboard=[
        *get_main_menu_back_button(),
    ])