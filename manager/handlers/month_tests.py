from aiogram import Router, F
from aiogram.types import CallbackQuery, Message
from aiogram.filters import StateFilter
from aiogram.fsm.context import FSMContext
from aiogram.fsm.state import State, StatesGroup

from manager.keyboards.month_tests import (
    get_month_tests_menu_kb,
    get_courses_for_tests_kb,
    get_subjects_for_tests_kb,
    get_microtopics_input_kb,
    get_confirm_test_creation_kb,
    get_tests_list_kb,
    get_confirm_delete_test_kb
)
from manager.keyboards.main import get_manager_main_menu_kb

router = Router()

class ManagerMonthTestsStates(StatesGroup):
    main = State()  # Главное меню тестов месяца
    select_course = State()  # Выбор курса
    select_subject = State()  # Выбор предмета
    enter_month_name = State()  # Ввод названия контрольного месяца
    enter_microtopics = State()  # Ввод номеров микротем
    confirm_creation = State()  # Подтверждение создания теста
    tests_list = State()  # Список созданных тестов
    confirm_deletion = State()  # Подтверждение удаления

# Временное хранилище данных (потом заменить на БД)
courses_db = {
    1: "ЕНТ",
    2: "IT"
}

subjects_db = {
    1: ["Математика", "Физика", "Информатика", "История Казахстана", "Химия", "Биология"],
    2: ["Python", "JavaScript", "Java"]
}

# Временное хранилище микротем (из topics.py)
topics_db = {
    "Математика": ["Дроби", "Проценты", "Алгебра", "Геометрия"],
    "Физика": ["Механика", "Оптика", "Электричество"],
    "Информатика": ["Алгоритмы", "Массивы", "Циклы"],
    "История Казахстана": ["Древний период", "Средневековье", "Новое время"],
    "Химия": ["Органическая химия", "Неорганическая химия", "Физическая химия"],
    "Биология": ["Ботаника", "Зоология", "Генетика"]
}

# Хранилище созданных тестов
created_tests = {}

@router.callback_query(F.data == "manager_month_tests")
async def show_month_tests_menu(callback: CallbackQuery, state: FSMContext):
    """Показываем главное меню тестов месяца"""
    await state.set_state(ManagerMonthTestsStates.main)
    await callback.message.edit_text(
        text="🧠 Управление входными и контрольными тестами месяца",
        reply_markup=get_month_tests_menu_kb()
    )

@router.callback_query(F.data == "create_month_test")
async def start_create_test(callback: CallbackQuery, state: FSMContext):
    """Начинаем создание нового теста"""
    await state.set_state(ManagerMonthTestsStates.select_course)
    await callback.message.edit_text(
        text="Выберите курс для создания теста:",
        reply_markup=get_courses_for_tests_kb()
    )

@router.callback_query(ManagerMonthTestsStates.select_course, F.data.startswith("course_"))
async def select_course(callback: CallbackQuery, state: FSMContext):
    """Обрабатываем выбор курса"""
    course_id = int(callback.data.replace("course_", ""))
    course_name = courses_db.get(course_id, "Неизвестный курс")
    
    await state.update_data(course_id=course_id, course_name=course_name)
    await state.set_state(ManagerMonthTestsStates.select_subject)
    
    await callback.message.edit_text(
        text=f"Курс: {course_name}\nВыберите предмет:",
        reply_markup=get_subjects_for_tests_kb(course_id)
    )

@router.callback_query(ManagerMonthTestsStates.select_subject, F.data.startswith("subject_"))
async def select_subject(callback: CallbackQuery, state: FSMContext):
    """Обрабатываем выбор предмета"""
    subject_name = callback.data.replace("subject_", "")
    
    data = await state.get_data()
    course_name = data.get("course_name", "")
    
    await state.update_data(subject_name=subject_name)
    await state.set_state(ManagerMonthTestsStates.enter_month_name)
    
    await callback.message.edit_text(
        text=f"Курс: {course_name}\n"
             f"Предмет: {subject_name}\n\n"
             f"Введите название контрольного месяца:",
        reply_markup=None
    )

@router.message(StateFilter(ManagerMonthTestsStates.enter_month_name))
async def process_month_name(message: Message, state: FSMContext):
    """Обрабатываем ввод названия месяца"""
    month_name = message.text.strip()
    
    data = await state.get_data()
    course_name = data.get("course_name", "")
    subject_name = data.get("subject_name", "")
    
    # Получаем доступные микротемы для предмета
    available_topics = topics_db.get(subject_name, [])
    
    if not available_topics:
        await message.answer(
            f"❌ Для предмета {subject_name} не найдено микротем.\n"
            f"Сначала добавьте микротемы через меню 'Микротемы'.",
            reply_markup=get_manager_main_menu_kb()
        )
        await state.clear()
        return
    
    # Формируем список микротем с номерами
    topics_list = ""
    for i, topic in enumerate(available_topics, 1):
        topics_list += f"{i}. {topic}\n"
    
    await state.update_data(month_name=month_name, available_topics=available_topics)
    await state.set_state(ManagerMonthTestsStates.enter_microtopics)
    
    await message.answer(
        text=f"Курс: {course_name}\n"
             f"Предмет: {subject_name}\n"
             f"Месяц: {month_name}\n\n"
             f"📝 Доступные микротемы:\n{topics_list}\n"
             f"Введите номера микротем через пробел (например: 1 3 4):",
        reply_markup=get_microtopics_input_kb()
    )
