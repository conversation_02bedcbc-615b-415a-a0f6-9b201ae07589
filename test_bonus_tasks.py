#!/usr/bin/env python3
"""
Тестовый скрипт для проверки системы бонусных заданий
"""

import asyncio
import sys
from unittest.mock import AsyncMock, MagicMock

# Добавляем путь к проекту
sys.path.append('.')

from manager.handlers.bonus_tasks import router, debug_bonus_tasks_handler, show_bonus_task_management
from aiogram.types import CallbackQuery, Message, User, Chat
from aiogram.fsm.context import FSMContext

async def test_bonus_tasks_handler():
    """Тест обработчика бонусных заданий"""
    print("🧪 Тестирование обработчика бонусных заданий...")
    
    # Создаем мок объекты
    user = User(id=123, is_bot=False, first_name="Test", username="test_user")
    chat = Chat(id=123, type="private")
    message = Message(
        message_id=1,
        date=None,
        chat=chat,
        from_user=user,
        content_type="text",
        options={}
    )
    
    # Создаем мок callback
    callback = CallbackQuery(
        id="test_callback",
        from_user=user,
        chat_instance="test_instance",
        data="manager_bonus_tasks",
        message=message
    )
    
    # Мокаем методы
    callback.message.edit_text = AsyncMock()
    callback.answer = AsyncMock()
    
    # Создаем мок состояния
    state = AsyncMock(spec=FSMContext)
    state.get_state = AsyncMock(return_value=None)
    state.set_state = AsyncMock()
    state.get_data = AsyncMock(return_value={})
    state.update_data = AsyncMock()
    
    try:
        # Тестируем обработчик
        await debug_bonus_tasks_handler(callback, state)
        print("✅ Обработчик выполнен без ошибок")
        
        # Проверяем, что методы были вызваны
        callback.message.edit_text.assert_called_once()
        state.set_state.assert_called_once()
        
        print("✅ Все проверки пройдены успешно!")
        
    except Exception as e:
        print(f"❌ Ошибка при тестировании: {e}")
        import traceback
        traceback.print_exc()

def test_router_registration():
    """Тест регистрации роутера"""
    print("🧪 Тестирование регистрации роутера...")
    
    # Проверяем, что роутер существует
    assert router is not None, "Роутер не создан"
    print("✅ Роутер создан")
    
    # Проверяем, что обработчики зарегистрированы
    handlers = router.callback_query.handlers
    print(f"📊 Количество зарегистрированных обработчиков callback_query: {len(handlers)}")
    
    # Ищем наш обработчик
    bonus_handler_found = False
    for handler in handlers:
        if hasattr(handler, 'callback') and handler.callback.__name__ == 'debug_bonus_tasks_handler':
            bonus_handler_found = True
            break
    
    if bonus_handler_found:
        print("✅ Обработчик бонусных заданий найден в роутере")
    else:
        print("❌ Обработчик бонусных заданий НЕ найден в роутере")
        print("📋 Список обработчиков:")
        for i, handler in enumerate(handlers):
            if hasattr(handler, 'callback'):
                print(f"  {i+1}. {handler.callback.__name__}")

async def main():
    """Главная функция тестирования"""
    print("🚀 Запуск тестов системы бонусных заданий\n")
    
    # Тест 1: Регистрация роутера
    test_router_registration()
    print()
    
    # Тест 2: Обработчик
    await test_bonus_tasks_handler()
    print()
    
    print("🎉 Тестирование завершено!")

if __name__ == "__main__":
    asyncio.run(main())
